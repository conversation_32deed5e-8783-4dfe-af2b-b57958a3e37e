% Clear workspace and close figures
clear; clc; close all;

% 1. --- Parameters for Synthetic Data ---
% These parameters are based on the visual information in Figure 1 of the paper.
altitude_min = 20;  % km
altitude_max = 40;  % km
resolution = 0.1;   % km, vertical resolution of the data
dominant_vertical_wavelength = 6.5; % km, a typical value from Fig 1c
noise_level = 0.2;  % Amplitude of random noise
true_phase_shift_deg = 45; % degrees, the ground-truth phase shift we want to recover

% 2. --- Generate Synthetic Temperature Fluctuation Profiles ---
% Create the altitude vector
altitude = (altitude_min:resolution:altitude_max-resolution)';
n_points = length(altitude);

% Create the primary signal component (a sine wave)
% Vertical wavenumber k = 2*pi / lambda
vertical_wavenumber = 2 * pi / dominant_vertical_wavelength;
sine_wave = sin(vertical_wavenumber * altitude);

% Create two temperature profiles (T1 and T2)
% T1 is the reference profile.
% T2 is the same as T1 but with a phase shift and some noise.
random_noise_1 = (rand(n_points, 1) - 0.5) * 2 * noise_level;
random_noise_2 = (rand(n_points, 1) - 0.5) * 2 * noise_level;

profile_1 = sine_wave + random_noise_1;
profile_2 = sin(vertical_wavenumber * altitude + deg2rad(true_phase_shift_deg)) + random_noise_2;

% 3. --- Sliding Window Cross-Spectral Analysis ---
% This section mimics the analysis done on segments of the data.
window_size_km = 10.0;  % Analysis window size in km.
window_size_pts = round(window_size_km / resolution); % Window size in data points.
step_size_pts = round(0.1 / resolution); % Slide the window by 100m (0.1 km)

% Arrays to store the results (pre-allocate for efficiency)
num_windows = floor((n_points - window_size_pts) / step_size_pts) + 1;
center_altitudes = zeros(num_windows, 1);
calculated_phases_deg = zeros(num_windows, 1);

% The sampling frequency for the spectral analysis is points per kilometer.
sampling_fs = 1 / resolution;
window_function = hann(window_size_pts); % Use a Hann window

% Loop through the data with a sliding window
count = 1;
for i = 1:step_size_pts:(n_points - window_size_pts + 1)
    % Define the current window
    window_start = i;
    window_end = i + window_size_pts - 1;
    
    % Get the center altitude of the current window
    current_altitude_segment = altitude(window_start:window_end);
    center_altitudes(count) = mean(current_altitude_segment);

    % Extract the data segments from the two profiles
    segment_1 = profile_1(window_start:window_end);
    segment_2 = profile_2(window_start:window_end);

    % Calculate the Cross-Spectral Density (CSD) using MATLAB's cpsd function
    % The CSD is a complex array; its angle gives the phase difference.
    [csd_complex, wavenumbers] = cpsd(segment_1, segment_2, window_function, 0, [], sampling_fs);

    % Find the wavenumber in our results that is closest to the dominant wavenumber
    % of our synthetic signal.
    target_wavenumber = 1 / dominant_vertical_wavelength;
    [~, dominant_wavenumber_index] = min(abs(wavenumbers - target_wavenumber));

    % Get the phase difference at that dominant wavenumber
    phase_rad = angle(csd_complex(dominant_wavenumber_index));
    phase_deg = rad2deg(phase_rad);
    
    % Store the result
    calculated_phases_deg(count) = phase_deg;
    count = count + 1;
end

% 4. --- Plot the Results ---
% Create a figure with two subplots, similar to Figure 1b and 1d.
figure('Name', 'Phase Difference Analysis', 'Position', [100 100 1000 500]);

% Plot 1: The two synthetic temperature profiles (like Figure 1b)
subplot(1, 2, 1);
plot(profile_1, altitude, 'r-', 'LineWidth', 1.5, 'DisplayName', 'Profile 1');
hold on;
plot(profile_2, altitude, 'b-', 'LineWidth', 1.5, 'DisplayName', 'Profile 2 (Shifted)');
title('Synthetic Temperature Fluctuation');
xlabel('Temperature Anomaly [K]');
ylabel('Altitude [km]');
legend('show', 'Location', 'best');
xlim([-1.5, 1.5]);
ylim([altitude_min, altitude_max]);
grid on;
set(gca, 'YDir','reverse'); % Flip y-axis to match Python plot
set(gca, 'YDir','normal');  % Reset y-axis direction

% Plot 2: The calculated phase difference profile (like Figure 1d)
subplot(1, 2, 2);
plot(calculated_phases_deg, center_altitudes, 'b-', 'LineWidth', 1.5, 'DisplayName', 'Calculated Phase');
hold on;
line([true_phase_shift_deg, true_phase_shift_deg], [altitude_min, altitude_max], ...
    'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5, ...
    'DisplayName', sprintf('True Phase (%.0f°)', true_phase_shift_deg));
title('Calculated Phase Difference');
xlabel('Phase Shift [deg]');
legend('show', 'Location', 'best');
xlim([-180, 180]);
ylim([altitude_min, altitude_max]);
grid on;

sgtitle('Phase Difference Analysis of Synthetic Temperature Profiles (MATLAB)', 'FontSize', 16);
