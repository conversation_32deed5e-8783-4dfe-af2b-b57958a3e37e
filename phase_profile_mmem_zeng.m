% Clear workspace and close figures
clear; clc; close all;

% 1. --- Load Real Temperature Profiles from File ---
% IMPORTANT: Place your data file 'Zeng_Blue_Red_Yellow.txt' in the same 
% folder as this script, or provide the full path to the file.
% This script will use the first two columns from the file.

data = readmatrix('Zeng_Blue_Red_Yellow.txt');
altitude = data(:, 1); % First profile
blue_profile = data(:, 2); % Second profile
red_profile = data(:, 3); % Second profile
yellow_profile = data(:, 4); % Second profile

% This script demonstrates calculating a phase profile and then correcting
% or "wrapping" the phase to the range of -90 to +90 degrees.
%
% This version uses a smaller window size (5 km) to demonstrate that the
% script's logic produces a profile, as the provided data file is not
% large enough for the paper's specified 20 km window.


% --- PARAMETERS FOR DEMONSTRATION ---
window_size_km = 5; % ILLUSTRATIVE window size of 5 km
disp(['--- NOTE: Using a DEMONSTRATION window size of ', num2str(window_size_km), ' km ---']);


altitude_step_km = 0.1;
ar_model_order = 10;
vertical_wavelength_km = 6.5; % From Figure 1c
sampling_interval_km = 0.1;

% Calculate window size in data points
window_size_points = round(window_size_km / sampling_interval_km);


% Initialize arrays to store the results
altitudes_for_plot = [];
phase_diff_blue_red = [];
phase_diff_blue_yellow = [];
phase_diff_red_yellow = [];

% Define the ARX model orders
model_orders = [ar_model_order ar_model_order 1];

% Loop through the data with a sliding window
for i = 1:(length(altitude) - window_size_points)
    
    % Define the current window
    window_indices = i:(i + window_size_points - 1);
    
    % Get the center altitude for the current window
    center_altitude = mean(altitude(window_indices));
    
    % --- Model fitting for each pair ---
    profile_pair_br = [blue_profile(window_indices), red_profile(window_indices)];
    data_id_br = iddata(profile_pair_br(:,2), profile_pair_br(:,1), sampling_interval_km);
    model_br = arx(data_id_br, model_orders);
    
    profile_pair_by = [blue_profile(window_indices), yellow_profile(window_indices)];
    data_id_by = iddata(profile_pair_by(:,2), profile_pair_by(:,1), sampling_interval_km);
    model_by = arx(data_id_by, model_orders);
    
    profile_pair_ry = [red_profile(window_indices), yellow_profile(window_indices)];
    data_id_ry = iddata(profile_pair_ry(:,2), profile_pair_ry(:,1), sampling_interval_km);
    model_ry = arx(data_id_ry, model_orders);
    
    % Get Frequency Response from the Models
    freq_response_br = idfrd(model_br);
    freq_response_by = idfrd(model_by);
    freq_response_ry = idfrd(model_ry);
    
    % Target frequency
    target_freq_rad_per_sample = 2 * pi * sampling_interval_km / vertical_wavelength_km;
    
    % Find the index of the closest frequency
    [~, freq_index] = min(abs(freq_response_br.Frequency - target_freq_rad_per_sample));
    
    % Get the phase at the target frequency
    phase_br = angle(squeeze(freq_response_br.ResponseData(1,1,freq_index))) * 180/pi;
    phase_by = angle(squeeze(freq_response_by.ResponseData(1,1,freq_index))) * 180/pi;
    phase_ry = angle(squeeze(freq_response_ry.ResponseData(1,1,freq_index))) * 180/pi;
    
    % --- NEW: CORRECT PHASE TO BE BETWEEN -90 and 90 DEGREES ---
    % This is also known as "phase wrapping".
    if phase_br > 90
        phase_br = phase_br - 180;
    elseif phase_br < -90
        phase_br = phase_br + 180;
    end
    
    if phase_by > 90
        phase_by = phase_by - 180;
    elseif phase_by < -90
        phase_by = phase_by + 180;
    end
    
    if phase_ry > 90
        phase_ry = phase_ry - 180;
    elseif phase_ry < -90
        phase_ry = phase_ry + 180;
    end

    % Store the results in an array
    altitudes_for_plot = [altitudes_for_plot; center_altitude];
    phase_diff_blue_red = [phase_diff_blue_red; phase_br];
    phase_diff_blue_yellow = [phase_diff_blue_yellow; phase_by];
    phase_diff_red_yellow = [phase_diff_red_yellow; phase_ry];
end


% Plot the results to generate a figure similar to Figure 1d
figure;
hold on;
plot(phase_diff_red_yellow, altitudes_for_plot, 'b', 'LineWidth', 1.5);
plot(phase_diff_blue_red, altitudes_for_plot, 'r', 'LineWidth', 1.5);
plot(phase_diff_blue_yellow, altitudes_for_plot, 'Color', [0.9290 0.6940 0.1250], 'LineWidth', 1.5); % Orange

% Formatting the plot
grid on;
xlabel('Phase shift [deg]');
ylabel('Altitude [km]');
title({'Corrected Phase Difference Profile (-90 to 90 deg)', ['Using ', num2str(window_size_km), ' km Window']});
legend('Red-Yellow (2-3)', 'Blue-Red (1-2)', 'Blue-Yellow (1-3)');
ylim([20 40]);
xlim([-90 90]); % Set x-axis limits to the new phase range
set(gca, 'YDir', 'normal');