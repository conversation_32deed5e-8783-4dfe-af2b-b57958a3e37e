function [altitude_centers, phase_differences_deg] = calculate_phase_profile_mmem(profile1, profile2, altitude_km, model_order, window_size_km, step_size_km)
    % Calculates the phase difference profile using the Multichannel Maximum
    % Entropy Method by fitting a multivariate autoregressive (AR) model.

    % --- Setup ---
    sampling_interval_km = altitude_km(2) - altitude_km(1);
    window_size_points = round(window_size_km / sampling_interval_km);
    step_size_points = max(1, round(step_size_km / sampling_interval_km));
    
    % **********************************************************************
    % ****** ROBUSTNESS CHECK: Prevent the overfitting warning ******
    % For a 2-channel ARX model, we need 4*model_order < window_size_points
    num_params = 4 * model_order;
    if num_params >= window_size_points
        error('MMEM:TooManyParameters', ...
              ['Model order is too high for the window size.\n', ...
               'Number of data points in window: %d\n', ...
               'Number of model parameters to estimate: %d (4 * %d)\n', ...
               'Suggestion: Decrease model_order or increase window_size_km.'], ...
               window_size_points, num_params, model_order);
    end
    % **********************************************************************
    
    num_points = length(altitude_km);
    
    % --- Running Window Analysis ---
    altitude_centers = [];
    phase_differences_deg = [];
    
    start_index = 1;
    while start_index + window_size_points - 1 <= num_points
        end_index = start_index + window_size_points - 1;
        
        segment1 = profile1(start_index:end_index);
        segment2 = profile2(start_index:end_index);
        
        center_altitude = mean(altitude_km(start_index:end_index));

        % --- Core MMEM Step ---
        data_segment = iddata([segment1, segment2], [], sampling_interval_km);
        
        na = ones(2, 2) * model_order;
        nb = [];
        nk = [];
        
        ar_model = arx(data_segment, [na, nb, nk]);
        
        [S, ~] = spectrum(ar_model);
        
        cross_spectrum_S12 = squeeze(S(1, 2, :));
        
        [~, dominant_freq_index] = max(abs(cross_spectrum_S12));
        phase_rad = angle(cross_spectrum_S12(dominant_freq_index));
        
        altitude_centers = [altitude_centers; center_altitude];
        phase_differences_deg = [phase_differences_deg; rad2deg(phase_rad)];
        
        start_index = start_index + step_size_points;
    end
end