This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  4 APR 2025 11:08
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./MJO_proposal_draft_New.tex
(MJO_proposal_draft_New.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/setspace\setspace.s
ty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/siunitx\siunitx.sty
Package: siunitx 2024-06-24 v3.3.19 A comprehensive (SI) units package
\l__siunitx_number_uncert_offset_int=\count271
\l__siunitx_number_exponent_fixed_int=\count272
\l__siunitx_number_min_decimal_int=\count273
\l__siunitx_number_min_integer_int=\count274
\l__siunitx_number_round_precision_int=\count275
\l__siunitx_number_lower_threshold_int=\count276
\l__siunitx_number_upper_threshold_int=\count277
\l__siunitx_number_group_first_int=\count278
\l__siunitx_number_group_size_int=\count279
\l__siunitx_number_group_minimum_int=\count280
\l__siunitx_angle_tmp_dim=\dimen150
\l__siunitx_angle_marker_box=\box52
\l__siunitx_angle_unit_box=\box53
\l__siunitx_compound_count_int=\count281

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations\transl
ations.sty
Package: translations 2022/02/05 v1.12 internationalization of LaTeX2e packages
 (CN)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count282
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen151
))
\l__siunitx_table_tmp_box=\box54
\l__siunitx_table_tmp_dim=\dimen152
\l__siunitx_table_column_width_dim=\dimen153
\l__siunitx_table_integer_box=\box55
\l__siunitx_table_decimal_box=\box56
\l__siunitx_table_uncert_box=\box57
\l__siunitx_table_before_box=\box58
\l__siunitx_table_after_box=\box59
\l__siunitx_table_before_dim=\dimen154
\l__siunitx_table_carry_dim=\dimen155
\l__siunitx_unit_tmp_int=\count283
\l__siunitx_unit_position_int=\count284
\l__siunitx_unit_total_int=\count285
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/06/14 v2.6d Tabular extension package (FMi)
\col@sep=\dimen156
\ar@mcellbox=\box60
\extrarowheight=\dimen157
\NC@list=\toks20
\extratabsurround=\skip51
\backup@length=\skip52
\ar@cellbox=\box61
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip53
For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen158
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count286
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count287
\leftroot@=\count288
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count289
\DOTSCASE@=\count290
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box62
\strutbox@=\box63
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen159
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count291
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count292
\dotsspace@=\muskip17
\c@parentequation=\count293
\dspbrk@lvl=\count294
\tag@help=\toks21
\row@=\count295
\column@=\count296
\maxfields@=\count297
\andhelp@=\toks22
\eqnshift@=\dimen160
\alignsep@=\dimen161
\tagshift@=\dimen162
\tagwidth@=\dimen163
\totwidth@=\dimen164
\lineht@=\dimen165
\@envbody=\toks23
\multlinegap=\skip54
\multlinetaggap=\skip55
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.st
y
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.s
ty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.s
ty
Package: enumitem 2025/01/19 v3.10 Customized lists
\labelindent=\skip56
\enit@outerparindent=\dimen166
\enit@toks=\toks25
\enit@inbox=\box64
\enit@count@id=\count298
\enitdp@description=\count299
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)
\TX@col@width=\dimen167
\TX@old@table=\dimen168
\TX@old@col=\dimen169
\TX@target=\dimen170
\TX@delta=\dimen171
\TX@cols=\count300
\TX@ftn=\toks26
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count301
\float@exts=\toks27
\float@box=\box65
\@float@everytoks=\toks28
\@floatcapt=\box66
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/diagbox\diagbox.sty
Package: diagbox 2020/02/09 v2.3 Making table heads with diagonal lines
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\pict2e.sty
Package: pict2e 2020/09/30 v0.4b Improved picture commands (HjG,RN,JT)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\pict2e.cfg
File: pict2e.cfg 2016/02/05 v0.1u pict2e configuration for teTeX/TeXLive
)
Package pict2e Info: Driver file: pdftex.def on input line 112.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
Package pict2e Info: Driver file for pict2e: p2e-pdftex.def on input line 114.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\p2e-pdftex.d
ef
File: p2e-pdftex.def 2016/02/05 v0.1u Driver-dependant file (RN,HjG,JT)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
\pIIe@GRAPH=\toks29
\@arclen=\dimen172
\@arcrad=\dimen173
\pIIe@tempdima=\dimen174
\pIIe@tempdimb=\dimen175
\pIIe@tempdimc=\dimen176
\pIIe@tempdimd=\dimen177
\pIIe@tempdime=\dimen178
\pIIe@tempdimf=\dimen179
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count302
\calc@Bcount=\count303
\calc@Adimen=\dimen180
\calc@Bdimen=\dimen181
\calc@Askip=\skip57
\calc@Bskip=\skip58
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count304
\calc@Cskip=\skip59
)
\diagbox@boxa=\box67
\diagbox@boxb=\box68
\diagbox@boxm=\box69
\diagbox@wd=\dimen182
\diagbox@ht=\dimen183
\diagbox@insepl=\dimen184
\diagbox@insepr=\dimen185
\diagbox@outsepl=\dimen186
\diagbox@outsepr=\dimen187
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption3.st
y
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen188
\captionmargin=\dimen189
\caption@leftmargin=\dimen190
\caption@rightmargin=\dimen191
\caption@width=\dimen192
\caption@indent=\dimen193
\caption@parindent=\dimen194
\caption@hangindent=\dimen195
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count305
\c@continuedfloat=\count306
Package caption Info: float package is loaded.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/placeins\placeins.s
ty
Package: placeins 2005/04/18  v 2.2
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen196
\Gin@req@width=\dimen197
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/natbib\natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip60
\bibsep=\skip61
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count307
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.s
ty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfesca
pe.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.st
y
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.s
ty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\ge
ttitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count308
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringe
nc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen198
\Hy@linkcounter=\count309
\Hy@pagecounter=\count310

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.s
ty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count311

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count312
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen199

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigint
calc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count313
\Field@Width=\dimen256
\Fld@charsize=\dimen257
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.s
ty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count314
\c@Item=\count315
\c@Hfootnote=\count316
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.de
f
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.
sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count317
\c@bookmark@seq@number=\count318

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\reru
nfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uni
quecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip62
)
Package translations Info: No language package found. I am going to use `englis
h' as default language. on input line 31.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\color.sty
Package: color 2024/01/14 v1.3d Standard LaTeX Color (DPC)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count319
\l__pdf_internal_box=\box70
) (MJO_proposal_draft_New.aux)
\openout1 = `MJO_proposal_draft_New.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 31.
LaTeX Font Info:    ... okay on input line 31.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations/dicts\
translations-basic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `tra
nslations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' f
or `english'. on input line 31.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count320
\scratchdimen=\dimen258
\scratchbox=\box71
\nofMPsegments=\count321
\nofMParguments=\count322
\everyMPshowfont=\toks30
\MPscratchCnt=\count323
\MPscratchDim=\dimen259
\MPnumerator=\count324
\makeMPintoPDFobject=\count325
\everyMPtoPDFconversion=\toks31
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.
Package hyperref Info: Link coloring OFF on input line 31.
 (MJO_proposal_draft_New.out) (MJO_proposal_draft_New.out)
\@outlinefile=\write3
\openout3 = `MJO_proposal_draft_New.out'.



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]
LaTeX Font Info:    Trying to load font information for U+msa on input line 62.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 62.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[2]

[3]

[4]

[5]
Overfull \hbox (2.60333pt too wide) in paragraph at lines 118--119
\OT1/cmr/m/n/10 ([]$\OT1/cmtt/m/n/10 https : / / www . esrl . noaa . gov / psd 
/ data / gridded / data . interp _ OLR . html$[]\OT1/cmr/m/n/10 ) will serve as
 the pri-
 []



[6]

[7]
<./FaberPicture1.jpg, id=221, 314.97675pt x 201.1515pt>
File: ./FaberPicture1.jpg Graphic file (type jpg)
<use ./FaberPicture1.jpg>
Package pdftex.def Info: ./FaberPicture1.jpg  used on input line 158.
(pdftex.def)             Requested size: 375.80544pt x 240.00246pt.


[8]

[9 <./FaberPicture1.jpg>]

[10]

[11]

[12]
Overfull \hbox (24.20514pt too wide) in paragraph at lines 251--252
\OT1/cmr/m/n/10 pa-ram-e-ters ($\OML/cmm/m/it/10 H[]; H[]$\OT1/cmr/m/n/10 ) fro
m pro-cessed RO pro-files; Cre-ate ini-tial MJO phase/amplitude
 []



[13]

[14] (MJO_proposal_draft_New.bbl

[15]

[16]

[17])

[18] (MJO_proposal_draft_New.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
 ***********
Package rerunfilecheck Info: File `MJO_proposal_draft_New.out' has not changed.

(rerunfilecheck)             Checksum: F6CC66E487A62881CB85EE866BCBDD95;1684.
 ) 
Here is how much of TeX's memory you used:
 16789 strings out of 473904
 325715 string characters out of 5724713
 1947908 words of memory out of 5000000
 39361 multiletter control sequences out of 15000+600000
 562754 words of font info for 54 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,18n,80p,1838b,1092s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\
tcrm1000.pk><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/publi
c/amsfonts/cm/cmbx10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/font
s/type1/public/amsfonts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Program
s/MiKTeX/fonts/type1/public/amsfonts/cm/cmbx7.pfb><C:/Users/<USER>/AppData/L
ocal/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmex10.pfb><C:/Users/<USER>
nde/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi10.pfb><C:
/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/c
mmi7.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/a
msfonts/cm/cmr10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/ty
pe1/public/amsfonts/cm/cmr12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiK
TeX/fonts/type1/public/amsfonts/cm/cmr5.pfb><C:/Users/<USER>/AppData/Local/P
rograms/MiKTeX/fonts/type1/public/amsfonts/cm/cmr7.pfb><C:/Users/<USER>/AppD
ata/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy10.pfb><C:/Users/<USER>
oyeTunde/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy7.pfb
><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/
cm/cmti10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/pub
lic/amsfonts/cm/cmtt10.pfb>
Output written on MJO_proposal_draft_New.pdf (18 pages, 458650 bytes).
PDF statistics:
 418 PDF objects out of 1000 (max. 8388607)
 117 named destinations out of 1000 (max. 500000)
 62 words of extra memory for PDF output out of 10000 (max. 10000000)

