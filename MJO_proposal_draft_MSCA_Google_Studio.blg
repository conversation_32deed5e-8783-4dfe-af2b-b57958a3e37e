This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: MJO_proposal_draft_MSCA_Google_Studio.aux
Reallocating 'name_of_file' (item size: 1) to 8 items.
The style file: apalike.bst
Reallocating 'name_of_file' (item size: 1) to 14 items.
Database file #1: ./mjo_ref.bib
Repeated entry---line 399 of file ./mjo_ref.bib
 : @incollection{alexander2010gravity
 :                                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 516 of file ./mjo_ref.bib
 : @article{alexander2008global
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 526 of file ./mjo_ref.bib
 : @article{alexander2010gravity
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 537 of file ./mjo_ref.bib
 : @article{alexander1998interpretations
 :                                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 548 of file ./mjo_ref.bib
 : @article{alexander2018mjo
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 570 of file ./mjo_ref.bib
 : @article{ayorinde2023
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 581 of file ./mjo_ref.bib
 : @article{ayorinde2024
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 593 of file ./mjo_ref.bib
 : @article{Ern2004
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 604 of file ./mjo_ref.bib
 : @article{Faber2013
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 615 of file ./mjo_ref.bib
 : @article{fritts2003review
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 625 of file ./mjo_ref.bib
 : @article{geller2016qbo
 :                       ,
I'm skipping whatever remains of this entry
Repeated entry---line 636 of file ./mjo_ref.bib
 : @article{ho2022using
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 646 of file ./mjo_ref.bib
 : @article{hood2020stratospheric
 :                               ,
I'm skipping whatever remains of this entry
Repeated entry---line 656 of file ./mjo_ref.bib
 : @article{hood2023qbo
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 666 of file ./mjo_ref.bib
 : @article{li2020saber
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 677 of file ./mjo_ref.bib
 : @article{Lu2015
 :                ,
I'm skipping whatever remains of this entry
Repeated entry---line 688 of file ./mjo_ref.bib
 : @article{madden1994observations
 :                                ,
I'm skipping whatever remains of this entry
Repeated entry---line 698 of file ./mjo_ref.bib
 : @article{martin2020impact
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 709 of file ./mjo_ref.bib
 : @article{moss2016does
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 720 of file ./mjo_ref.bib
 : @article{Schmidt2016
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 742 of file ./mjo_ref.bib
 : @article{trencham2024causes
 :                            ,
I'm skipping whatever remains of this entry
Repeated entry---line 752 of file ./mjo_ref.bib
 : @article{Tsuda2000
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 763 of file ./mjo_ref.bib
 : @article{tsuchiya2016mjo
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 774 of file ./mjo_ref.bib
 : @article{wilson1991
 :                    ,
I'm skipping whatever remains of this entry
Repeated entry---line 785 of file ./mjo_ref.bib
 : @article{zhang2005madden
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 795 of file ./mjo_ref.bib
 : @article{zhou2024observed
 :                          ,
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "ayorinde2016"
You've used 26 entries,
            1935 wiz_defined-function locations,
            810 strings with 11386 characters,
and the built_in function-call counts, 12397 in all, are:
= -- 1170
> -- 649
< -- 8
+ -- 236
- -- 230
* -- 1215
:= -- 2165
add.period$ -- 78
call.type$ -- 26
change.case$ -- 244
chr.to.int$ -- 26
cite$ -- 26
duplicate$ -- 362
empty$ -- 803
format.name$ -- 264
if$ -- 2346
int.to.chr$ -- 1
int.to.str$ -- 0
missing$ -- 26
newline$ -- 133
num.names$ -- 78
pop$ -- 184
preamble$ -- 1
purify$ -- 244
quote$ -- 0
skip$ -- 272
stack$ -- 0
substring$ -- 998
swap$ -- 26
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 156
warning$ -- 0
while$ -- 90
width$ -- 0
write$ -- 340
(There were 26 error messages)
