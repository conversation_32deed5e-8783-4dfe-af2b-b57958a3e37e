This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: MJO_proposal_draft_MSCA.aux
Reallocating 'name_of_file' (item size: 1) to 8 items.
The style file: apalike.bst
Reallocating 'name_of_file' (item size: 1) to 14 items.
Database file #1: ./mjo_ref.bib
Repeated entry---line 399 of file ./mjo_ref.bib
 : @incollection{alexander2010gravity
 :                                   ,
I'm skipping whatever remains of this entry
You've used 31 entries,
            1935 wiz_defined-function locations,
            837 strings with 13080 characters,
and the built_in function-call counts, 16222 in all, are:
= -- 1493
> -- 1011
< -- 8
+ -- 383
- -- 368
* -- 1632
:= -- 2855
add.period$ -- 93
call.type$ -- 31
change.case$ -- 337
chr.to.int$ -- 30
cite$ -- 31
duplicate$ -- 432
empty$ -- 950
format.name$ -- 409
if$ -- 3026
int.to.chr$ -- 2
int.to.str$ -- 0
missing$ -- 32
newline$ -- 158
num.names$ -- 93
pop$ -- 273
preamble$ -- 1
purify$ -- 338
quote$ -- 0
skip$ -- 320
stack$ -- 0
substring$ -- 1185
swap$ -- 32
text.length$ -- 1
text.prefix$ -- 0
top$ -- 0
type$ -- 184
warning$ -- 0
while$ -- 109
width$ -- 0
write$ -- 405
(There was 1 error message)
