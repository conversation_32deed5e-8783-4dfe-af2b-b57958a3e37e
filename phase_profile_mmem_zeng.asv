% Clear workspace and close figures
clear; clc; close all;

% 1. --- Load Real Temperature Profiles from File ---
% IMPORTANT: Place your data file 'Zeng_Blue_Red_Yellow.txt' in the same 
% folder as this script, or provide the full path to the file.
% The file should be a text file with three columns of data and no headers.

data = readmatrix('Zeng_Blue_Red_Yellow.txt');
profile_1 = data(:, 2); % Blue profile in the paper (column 1)
profile_2 = data(:, 3); % Red profile in the paper (column 2)
profile_3 = data(:, 4); % Yellow profile in the paper (column 3)


% Generate altitude vector to match the exact length of the data profiles.
n_data_points = length(profile_1);
altitude = linspace(20, 40, n_data_points)'; % From 20 to 40 km

% 2. --- Sliding Window Cross-Spectral Analysis ---
window_size_km = 10.0;  % Analysis window size in km.
resolution = altitude(2) - altitude(1); % Calculate resolution from data
window_size_pts = round(window_size_km / resolution); % Window size in data points.
step_size_pts = round(0.1 / resolution); % Slide the window by 100m (0.1 km)

% Arrays to store the results (pre-allocate for efficiency)
n_points = length(altitude);
num_windows = floor((n_points - window_size_pts) / step_size_pts) + 1;
center_altitudes = zeros(num_windows, 1);
phase_12 = zeros(num_windows, 1); % Phase between profile 1 and 2
phase_13 = zeros(num_windows, 1); % Phase between profile 1 and 3
phase_23 = zeros(num_windows, 1); % Phase between profile 2 and 3


% The sampling frequency for the spectral analysis is points per kilometer.
sampling_fs = 1 / resolution;
window_function = hann(window_size_pts); % Use a Hann window

% Loop through the data with a sliding window
count = 1;
for i = 1:step_size_pts:(n_points - window_size_pts + 1)
    % Define the current window
    window_start = i;
    window_end = i + window_size_pts - 1;

    % Get the center altitude of the current window
    current_altitude_segment = altitude(window_start:window_end);
    center_altitudes(count) = mean(current_altitude_segment);

    % Extract the data segments from the three profiles
    seg_1 = profile_1(window_start:window_end);
    seg_2 = profile_2(window_start:window_end);
    seg_3 = profile_3(window_start:window_end);

    % --- Calculate Phase for Pair 1-2 (Blue line in paper's phase plot) ---
    [csd12, ~] = cpsd(seg_1, seg_2, window_function, 0, [], sampling_fs);
    [~, idx12] = max(abs(csd12));
    phase_12(count) = rad2deg(angle(csd12(idx12)));

    % --- Calculate Phase for Pair 1-3 (Red line in paper's phase plot) ---
    [csd13, ~] = cpsd(seg_1, seg_3, window_function, 0, [], sampling_fs);
    [~, idx13] = max(abs(csd13));
    phase_13(count) = rad2deg(angle(csd13(idx13)));

    % --- Calculate Phase for Pair 2-3 (Orange/Yellow line in paper's phase plot) ---
    [csd23, ~] = cpsd(seg_2, seg_3, window_function, 0, [], sampling_fs);
    [~, idx23] = max(abs(csd23));
    phase_23(count) = rad2deg(angle(csd23(idx23)));

    count = count + 1;
end

% 3. --- Plot the Results ---
figure('Name', 'Phase Difference Analysis of Real Data', 'Position', [100 100 1000 500]);

% Plot 1: The three real temperature profiles (like Figure 1b)
subplot(1, 2, 1);
hold on;
plot(profile_1, altitude, 'b-', 'LineWidth', 1.5, 'DisplayName', 'Profile 1 (Blue)');
plot(profile_2, altitude, 'r-', 'LineWidth', 1.5, 'DisplayName', 'Profile 2 (Red)');
plot(profile_3, altitude, 'color', [0.93,0.69,0.13], 'LineWidth', 1.5, 'DisplayName', 'Profile 3 (Yellow)');
title('Real Temperature Fluctuation Profiles');
xlabel('Temperature Anomaly [K]');
ylabel('Altitude [km]');
legend('show', 'Location', 'best');
xlim([-3, 3]); % Adjust x-axis limits for the real data range
ylim([20, 40]);
grid on;
hold off;

% Plot 2: The calculated phase difference profiles (like Figure 1d)
subplot(1, 2, 2);
hold on;
plot(phase_12, center_altitudes, 'b-', 'LineWidth', 1.5, 'DisplayName', 'Phase (1-2)');
plot(phase_13, center_altitudes, 'r-', 'LineWidth', 1.5, 'DisplayName', 'Phase (1-3)');
plot(phase_23, center_altitudes, 'color', [0.93,0.69,0.13], 'LineWidth', 1.5, 'DisplayName', 'Phase (2-3)');
title('Calculated Phase Difference');
xlabel('Phase Shift [deg]');
legend('show', 'Location', 'best');
xlim([-180, 180]);
ylim([20, 40]);
grid on;
hold off;

sgtitle('Phase Difference Analysis of Real Data (MATLAB)', 'FontSize', 16);
