This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: MJO_proposal_draft_MSCA_New1_sub.aux
Reallocating 'name_of_file' (item size: 1) to 8 items.
The style file: apalike.bst
Reallocating 'name_of_file' (item size: 1) to 18 items.
Database file #1: ./mjo_ref_sub.bib
I was expecting a `,' or a `}'---line 127 of file ./mjo_ref_sub.bib
 :  doi = {10.1186/s40623-024-02065-y} 
 :                                     % Found DOI online, check if correct
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "Faber2013"
You've used 22 entries,
            1935 wiz_defined-function locations,
            639 strings with 9173 characters,
and the built_in function-call counts, 11162 in all, are:
= -- 1039
> -- 644
< -- 5
+ -- 238
- -- 232
* -- 1117
:= -- 1954
add.period$ -- 66
call.type$ -- 22
change.case$ -- 226
chr.to.int$ -- 22
cite$ -- 22
duplicate$ -- 308
empty$ -- 689
format.name$ -- 260
if$ -- 2097
int.to.chr$ -- 1
int.to.str$ -- 0
missing$ -- 22
newline$ -- 113
num.names$ -- 66
pop$ -- 177
preamble$ -- 1
purify$ -- 226
quote$ -- 0
skip$ -- 226
stack$ -- 0
substring$ -- 868
swap$ -- 22
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 132
warning$ -- 0
while$ -- 79
width$ -- 0
write$ -- 288
(There was 1 error message)
