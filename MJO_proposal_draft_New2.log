This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  3 APR 2025 09:11
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./MJO_proposal_draft_New2.tex
(MJO_proposal_draft_New2.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size12.clo
File: size12.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/setspace\setspace.s
ty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen150
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen151
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen152
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip17
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks20
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks21
\eqnshift@=\dimen153
\alignsep@=\dimen154
\tagshift@=\dimen155
\tagwidth@=\dimen156
\totwidth@=\dimen157
\lineht@=\dimen158
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.st
y
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.s
ty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/siunitx\siunitx.sty
Package: siunitx 2024-06-24 v3.3.19 A comprehensive (SI) units package
\l__siunitx_number_uncert_offset_int=\count283
\l__siunitx_number_exponent_fixed_int=\count284
\l__siunitx_number_min_decimal_int=\count285
\l__siunitx_number_min_integer_int=\count286
\l__siunitx_number_round_precision_int=\count287
\l__siunitx_number_lower_threshold_int=\count288
\l__siunitx_number_upper_threshold_int=\count289
\l__siunitx_number_group_first_int=\count290
\l__siunitx_number_group_size_int=\count291
\l__siunitx_number_group_minimum_int=\count292
\l__siunitx_angle_tmp_dim=\dimen159
\l__siunitx_angle_marker_box=\box54
\l__siunitx_angle_unit_box=\box55
\l__siunitx_compound_count_int=\count293

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations\transl
ations.sty
Package: translations 2022/02/05 v1.12 internationalization of LaTeX2e packages
 (CN)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count294
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
\l__siunitx_table_tmp_box=\box56
\l__siunitx_table_tmp_dim=\dimen160
\l__siunitx_table_column_width_dim=\dimen161
\l__siunitx_table_integer_box=\box57
\l__siunitx_table_decimal_box=\box58
\l__siunitx_table_uncert_box=\box59
\l__siunitx_table_before_box=\box60
\l__siunitx_table_after_box=\box61
\l__siunitx_table_before_dim=\dimen162
\l__siunitx_table_carry_dim=\dimen163
\l__siunitx_unit_tmp_int=\count295
\l__siunitx_unit_position_int=\count296
\l__siunitx_unit_total_int=\count297

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/06/14 v2.6d Tabular extension package (FMi)
\col@sep=\dimen164
\ar@mcellbox=\box62
\extrarowheight=\dimen165
\NC@list=\toks24
\extratabsurround=\skip54
\backup@length=\skip55
\ar@cellbox=\box63
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen166
\Gin@req@width=\dimen167
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.s
ty
Package: enumitem 2025/01/19 v3.10 Customized lists
\labelindent=\skip56
\enit@outerparindent=\dimen168
\enit@toks=\toks25
\enit@inbox=\box64
\enit@count@id=\count298
\enitdp@description=\count299
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/diagbox\diagbox.sty
Package: diagbox 2020/02/09 v2.3 Making table heads with diagonal lines
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\pict2e.sty
Package: pict2e 2020/09/30 v0.4b Improved picture commands (HjG,RN,JT)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\pict2e.cfg
File: pict2e.cfg 2016/02/05 v0.1u pict2e configuration for teTeX/TeXLive
)
Package pict2e Info: Driver file: pdftex.def on input line 112.
Package pict2e Info: Driver file for pict2e: p2e-pdftex.def on input line 114.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\p2e-pdftex.d
ef
File: p2e-pdftex.def 2016/02/05 v0.1u Driver-dependant file (RN,HjG,JT)
)
\pIIe@GRAPH=\toks26
\@arclen=\dimen169
\@arcrad=\dimen170
\pIIe@tempdima=\dimen171
\pIIe@tempdimb=\dimen172
\pIIe@tempdimc=\dimen173
\pIIe@tempdimd=\dimen174
\pIIe@tempdime=\dimen175
\pIIe@tempdimf=\dimen176
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count300
\calc@Bcount=\count301
\calc@Adimen=\dimen177
\calc@Bdimen=\dimen178
\calc@Askip=\skip57
\calc@Bskip=\skip58
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count302
\calc@Cskip=\skip59
)
\diagbox@boxa=\box65
\diagbox@boxb=\box66
\diagbox@boxm=\box67
\diagbox@wd=\dimen179
\diagbox@ht=\dimen180
\diagbox@insepl=\dimen181
\diagbox@insepr=\dimen182
\diagbox@outsepl=\dimen183
\diagbox@outsepr=\dimen184
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption3.st
y
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen185
\captionmargin=\dimen186
\caption@leftmargin=\dimen187
\caption@rightmargin=\dimen188
\caption@width=\dimen189
\caption@indent=\dimen190
\caption@parindent=\dimen191
\caption@hangindent=\dimen192
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count303
\c@continuedfloat=\count304
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/placeins\placeins.s
ty
Package: placeins 2005/04/18  v 2.2
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/natbib\natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip60
\bibsep=\skip61
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count305
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.s
ty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfesca
pe.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.st
y
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.s
ty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\ge
ttitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count306
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringe
nc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen193
\Hy@linkcounter=\count307
\Hy@pagecounter=\count308

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.s
ty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count309

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count310
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen194

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigint
calc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count311
\Field@Width=\dimen195
\Fld@charsize=\dimen196
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.s
ty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count312
\c@Item=\count313
\c@Hfootnote=\count314
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.de
f
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.
sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count315
\c@bookmark@seq@number=\count316

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\reru
nfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uni
quecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip62
)
Package hyperref Info: Option `colorlinks' set `true' on input line 40.
Package translations Info: No language package found. I am going to use `englis
h' as default language. on input line 43.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\color.sty
Package: color 2024/01/14 v1.3d Standard LaTeX Color (DPC)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count317
\l__pdf_internal_box=\box68
)
No file MJO_proposal_draft_New2.aux.
\openout1 = `MJO_proposal_draft_New2.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations/dicts\
translations-basic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `tra
nslations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' f
or `english'. on input line 43.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count318
\scratchdimen=\dimen197
\scratchbox=\box69
\nofMPsegments=\count319
\nofMParguments=\count320
\everyMPshowfont=\toks27
\MPscratchCnt=\count321
\MPscratchDim=\dimen198
\MPnumerator=\count322
\makeMPintoPDFobject=\count323
\everyMPtoPDFconversion=\toks28
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.
Package hyperref Info: Link coloring ON on input line 43.
 (MJO_proposal_draft_New2.out) (MJO_proposal_draft_New2.out)
\@outlinefile=\write3
\openout3 = `MJO_proposal_draft_New2.out'.



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]

Package natbib Warning: Citation `zhang2005madden' on page 2 undefined on input
 line 65.


Package natbib Warning: Citation `fritts2003review' on page 2 undefined on inpu
t line 65.


Package natbib Warning: Citation `alexander2010gravity' on page 2 undefined on 
input line 65.


Package natbib Warning: Citation `alexander2010gravity' on page 2 undefined on 
input line 67.



[2]
Overfull \hbox (4.36835pt too wide) in paragraph at lines 69--70
[]\OT1/cmr/m/n/12 This re-search di-rectly tack-les these is-sues, align-ing wi
th NASA's goals within the \OT1/cmr/bx/n/12 Weather
 []


Package natbib Warning: Citation `madden1971detection' on page 3 undefined on i
nput line 74.


Package natbib Warning: Citation `madden1994observations' on page 3 undefined o
n input line 74.


Package natbib Warning: Citation `andrews1987middle' on page 3 undefined on inp
ut line 74.


Package natbib Warning: Citation `fritts2003review' on page 3 undefined on inpu
t line 74.



[3]

Package natbib Warning: Citation `alexander2010gravity' on page 4 undefined on 
input line 76.


Package natbib Warning: Citation `tsuchiya2016mjo' on page 4 undefined on input
 line 76.


Package natbib Warning: Citation `alexander2018mjo' on page 4 undefined on inpu
t line 76.


Package natbib Warning: Citation `li2020saber' on page 4 undefined on input lin
e 76.


Package natbib Warning: Citation `cohen2016modulation' on page 4 undefined on i
nput line 76.


Package natbib Warning: Citation `zhou2024observed' on page 4 undefined on inpu
t line 76.


Package natbib Warning: Citation `cohen2016modulation' on page 4 undefined on i
nput line 76.


Package natbib Warning: Citation `moss2016does' on page 4 undefined on input li
ne 76.


Package natbib Warning: Citation `alexander2018mjo' on page 4 undefined on inpu
t line 76.


Package natbib Warning: Citation `ayorinde2018gravity' on page 4 undefined on i
nput line 76.


Package natbib Warning: Citation `ayorinde2021gravity' on page 4 undefined on i
nput line 76.


Package natbib Warning: Citation `ayorinde2022characteristics' on page 4 undefi
ned on input line 76.


Package natbib Warning: Citation `moss2016does' on page 4 undefined on input li
ne 78.


Package natbib Warning: Citation `alexander2018mjo' on page 4 undefined on inpu
t line 78.


Package natbib Warning: Citation `alexander2008global' on page 4 undefined on i
nput line 78.


Package natbib Warning: Citation `martin2020impact' on page 4 undefined on inpu
t line 78.


Package natbib Warning: Citation `tsuchiya2016mjo' on page 4 undefined on input
 line 78.


Package natbib Warning: Citation `li2020saber' on page 4 undefined on input lin
e 78.


Package natbib Warning: Citation `zhou2024observed' on page 4 undefined on inpu
t line 78.



[4]

Package natbib Warning: Citation `alexander2010gravity' on page 5 undefined on 
input line 80.


Package natbib Warning: Citation `moss2016does' on page 5 undefined on input li
ne 80.

LaTeX Font Info:    Trying to load font information for U+msa on input line 80.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 80.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[5]

[6]

Package natbib Warning: Citation `Faber2013' on page 7 undefined on input line 
105.


Package natbib Warning: Citation `alexander2008global' on page 7 undefined on i
nput line 105.


Package natbib Warning: Citation `Ern2004' on page 7 undefined on input line 10
5.



[7]

Package natbib Warning: Citation `ho2020overview' on page 8 undefined on input 
line 126.


Package natbib Warning: Citation `hersbach2020era5' on page 8 undefined on inpu
t line 127.


Package natbib Warning: Citation `wheeler2004all' on page 8 undefined on input 
line 128.


Package natbib Warning: Citation `emmert2021nrlmsis' on page 8 undefined on inp
ut line 129.



[8]

Package natbib Warning: Citation `alexander1998interpretations' on page 9 undef
ined on input line 136.


Package natbib Warning: Citation `Wang2009' on page 9 undefined on input line 1
36.


Package natbib Warning: Citation `Tsuda2000' on page 9 undefined on input line 
138.


Overfull \hbox (16.91127pt too wide) in paragraph at lines 138--139
[]\OT1/cmr/m/it/12 Potential En-ergy: \OT1/cmr/m/n/12 Cal-cu-late GW po-ten-tia
l en-ergy per unit mass $\OML/cmm/m/it/12 E[]\OT1/cmr/m/n/12 (\OML/cmm/m/it/12 
z\OT1/cmr/m/n/12 ) = [](\OML/cmm/m/it/12 g=N\OT1/cmr/m/n/12 )[](\OML/cmm/m/it/1
2 T[]=[]\OT1/cmr/m/n/12 )[]$
 []


Package natbib Warning: Citation `hindley2015southern' on page 9 undefined on i
nput line 139.


Package natbib Warning: Citation `fritts2003review' on page 9 undefined on inpu
t line 139.


Package natbib Warning: Citation `Ern2004' on page 9 undefined on input line 14
0.


Package natbib Warning: Citation `alexander2008global' on page 9 undefined on i
nput line 140.


Package natbib Warning: Citation `Faber2013' on page 9 undefined on input line 
140.


Package natbib Warning: Citation `Schmidt2016' on page 9 undefined on input lin
e 140.


Overfull \hbox (2.04549pt too wide) in paragraph at lines 140--141
[]\OT1/cmr/m/it/12 Momentum Flux Es-ti-ma-tion: \OT1/cmr/m/n/12 Es-ti-mate GWMF
 com-po-nents $(\OML/cmm/m/it/12 F[]; F[]\OT1/cmr/m/n/12 ) \OMS/cmsy/m/n/12 ^^Y
 \OML/cmm/m/it/12 ^^Z[]\OT1/cmr/m/n/12 (\OML/cmm/m/it/12 k; l\OT1/cmr/m/n/12 )\
OML/cmm/m/it/12 =mE[]$
 []


Package natbib Warning: Citation `Lu2015' on page 9 undefined on input line 141
.


Package natbib Warning: Citation `Faber2013' on page 9 undefined on input line 
148.


Package natbib Warning: Citation `Faber2013' on page 9 undefined on input line 
148.



[9]

Package natbib Warning: Citation `moss2016does' on page 10 undefined on input l
ine 160.


Package natbib Warning: Citation `alexander2018mjo' on page 10 undefined on inp
ut line 170.



[10]

Package natbib Warning: Citation `moss2016does' on page 11 undefined on input l
ine 188.


Package natbib Warning: Citation `Lu2015' on page 11 undefined on input line 18
9.



[11]

[12]

[13]

[14]
Overfull \hbox (3.14961pt too wide) in paragraph at lines 223--224
\OT1/cmr/bx/n/12 Re-port-ing: \OT1/cmr/m/n/12 Re-fine/implement GWMF es-ti-ma-t
ion; Ini-tial GWMF
 []


Overfull \hbox (21.12827pt too wide) in paragraph at lines 224--225
[]\OT1/cmr/bx/n/12 Comprehensive Anal-y-sis (GWMF, Mech-a-nisms, QBO/ENSO
 []


Overfull \hbox (18.15512pt too wide) in paragraph at lines 224--225
\OT1/cmr/bx/n/12 In-ter-ac-tions) and Manuscript 1 Prepa-ra-tion: \OT1/cmr/m/n/
12 Com-plete GWMF
 []


Overfull \hbox (14.35889pt too wide) in paragraph at lines 224--225
\OT1/cmr/m/n/12 anal-y-sis; Fi-nal-ize mech-a-nism dis-en-tan-gle-ment; Con-duc
t QBO/ENSO
 []


Overfull \hbox (11.38663pt too wide) in paragraph at lines 226--227
\OT1/cmr/bx/n/12 Model Eval-u-a-tion In-put, Fi-nal Re-port-ing, and Data Archi
v-
 []


LaTeX Warning: Reference `Tab:chronogram' on page 15 undefined on input line 23
0.


LaTeX Warning: `!h' float specifier changed to `!ht'.



[15] (MJO_proposal_draft_New2.bbl

[16]

[17]

[18])

Package natbib Warning: There were undefined citations.



[19] (MJO_proposal_draft_New2.aux

Package natbib Warning: Citation(s) may have changed.
(natbib)                Rerun to get citations correct.


! Package natbib Error: Bibliography not compatible with author-year citations.

(natbib)                Press <return> to continue in numerical citation style.


See the natbib package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.79 ...mand\NAT@force@numbers{}\NAT@force@numbers
                                                  
Check the bibliography entries for non-compliant syntax,
or select author-year BibTeX style, e.g. plainnat

)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
 ***********


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

Package rerunfilecheck Info: File `MJO_proposal_draft_New2.out' has not changed
.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 16645 strings out of 473904
 323275 string characters out of 5724713
 1938908 words of memory out of 5000000
 39225 multiletter control sequences out of 15000+600000
 565085 words of font info for 62 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,16n,80p,1966b,1062s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\
tcrm1200.pk><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/publi
c/amsfonts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/font
s/type1/public/amsfonts/cm/cmex10.pfb><C:/Users/<USER>/AppData/Local/Program
s/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi12.pfb><C:/Users/<USER>/AppData/
Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi8.pfb><C:/Users/<USER>
nde/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr12.pfb><C:/
Users/ToyeTunde/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cm
r8.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/ams
fonts/cm/cmsy10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/typ
e1/public/amsfonts/cm/cmsy8.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKT
eX/fonts/type1/public/amsfonts/cm/cmti12.pfb><C:/Users/<USER>/AppData/Local/
Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmtt12.pfb>
Output written on MJO_proposal_draft_New2.pdf (19 pages, 186370 bytes).
PDF statistics:
 261 PDF objects out of 1000 (max. 8388607)
 114 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

