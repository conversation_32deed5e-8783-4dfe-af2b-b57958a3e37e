This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: MJO_proposal_draft_MSCA2.aux
Reallocating 'name_of_file' (item size: 1) to 8 items.
The style file: apalike.bst
Reallocating 'name_of_file' (item size: 1) to 14 items.
Database file #1: ./mjo_ref.bib
Repeated entry---line 399 of file ./mjo_ref.bib
 : @incollection{alexander2010gravity
 :                                   ,
I'm skipping whatever remains of this entry
You've used 17 entries,
            1935 wiz_defined-function locations,
            753 strings with 9556 characters,
and the built_in function-call counts, 8357 in all, are:
= -- 774
> -- 485
< -- 4
+ -- 178
- -- 174
* -- 830
:= -- 1475
add.period$ -- 51
call.type$ -- 17
change.case$ -- 171
chr.to.int$ -- 17
cite$ -- 17
duplicate$ -- 236
empty$ -- 512
format.name$ -- 197
if$ -- 1568
int.to.chr$ -- 1
int.to.str$ -- 0
missing$ -- 17
newline$ -- 88
num.names$ -- 51
pop$ -- 133
preamble$ -- 1
purify$ -- 171
quote$ -- 0
skip$ -- 178
stack$ -- 0
substring$ -- 612
swap$ -- 17
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 102
warning$ -- 0
while$ -- 57
width$ -- 0
write$ -- 223
(There was 1 error message)
