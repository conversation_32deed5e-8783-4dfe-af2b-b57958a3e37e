This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: MJO_proposal_draft_MSCA_Google_New_copy.aux
Reallocating 'name_of_file' (item size: 1) to 8 items.
The style file: apalike.bst
Reallocating 'name_of_file' (item size: 1) to 14 items.
Database file #1: ./mjo_ref.bib
Repeated entry---line 399 of file ./mjo_ref.bib
 : @incollection{alexander2010gravity
 :                                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 556 of file ./mjo_ref.bib
 : @article{alexander2010gravity
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 567 of file ./mjo_ref.bib
 : @article{alexander1998interpretations
 :                                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 578 of file ./mjo_ref.bib
 : @article{alexander2018mjo
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 600 of file ./mjo_ref.bib
 : @article{ayorinde2023
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 611 of file ./mjo_ref.bib
 : @article{ayorinde2024
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 634 of file ./mjo_ref.bib
 : @article{Faber2013
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 645 of file ./mjo_ref.bib
 : @article{fritts2003review
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 666 of file ./mjo_ref.bib
 : @article{ho2022using
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 676 of file ./mjo_ref.bib
 : @article{hood2020stratospheric
 :                               ,
I'm skipping whatever remains of this entry
Repeated entry---line 686 of file ./mjo_ref.bib
 : @article{hood2023qbo
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 696 of file ./mjo_ref.bib
 : @article{li2020saber
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 718 of file ./mjo_ref.bib
 : @article{madden1994observations
 :                                ,
I'm skipping whatever remains of this entry
Repeated entry---line 772 of file ./mjo_ref.bib
 : @article{trencham2024causes
 :                            ,
I'm skipping whatever remains of this entry
Repeated entry---line 782 of file ./mjo_ref.bib
 : @article{Tsuda2000
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 793 of file ./mjo_ref.bib
 : @article{tsuchiya2016mjo
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 804 of file ./mjo_ref.bib
 : @article{wilson1991
 :                    ,
I'm skipping whatever remains of this entry
Repeated entry---line 815 of file ./mjo_ref.bib
 : @article{zhang2005madden
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 825 of file ./mjo_ref.bib
 : @article{zhou2024observed
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 934 of file ./mjo_ref.bib
 : @article{hood2020stratospheric
 :                               ,
I'm skipping whatever remains of this entry
Repeated entry---line 945 of file ./mjo_ref.bib
 : @article{hood2023qbo
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 956 of file ./mjo_ref.bib
 : @article{trencham2024causes
 :                            ,
I'm skipping whatever remains of this entry
You've used 26 entries,
            1935 wiz_defined-function locations,
            811 strings with 11561 characters,
and the built_in function-call counts, 12424 in all, are:
= -- 1183
> -- 629
< -- 9
+ -- 228
- -- 222
* -- 1216
:= -- 2156
add.period$ -- 78
call.type$ -- 26
change.case$ -- 241
chr.to.int$ -- 26
cite$ -- 26
duplicate$ -- 363
empty$ -- 817
format.name$ -- 258
if$ -- 2358
int.to.chr$ -- 1
int.to.str$ -- 0
missing$ -- 26
newline$ -- 133
num.names$ -- 78
pop$ -- 180
preamble$ -- 1
purify$ -- 241
quote$ -- 0
skip$ -- 271
stack$ -- 0
substring$ -- 1042
swap$ -- 26
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 156
warning$ -- 0
while$ -- 93
width$ -- 0
write$ -- 340
(There were 22 error messages)
